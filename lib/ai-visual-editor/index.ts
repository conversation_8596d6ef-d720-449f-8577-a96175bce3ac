// Main components
export { AIVisualEditor } from './components/ai-visual-editor'
export { AIVisualEditorLayout } from './components/ai-visual-editor-layout'
export { AiChatPanel } from './components/ai-chat-panel'
export { PropertiesPanel } from './components/properties-panel'
export { LivePreview } from './components/live-preview'
export { ComponentTree } from './components/component-tree'
export { DynamicComponentRenderer } from './components/dynamic-component-renderer'
export { NextJSLayoutGenerator } from './components/nextjs-layout-generator'
export { ComponentLibrary } from './components/component-library'

// Store and hooks
export { useEditorStore, useSelectedComponent, useSelectedComponentProperties, useComponentById, useComponentProperties } from './stores/editor-store'

// Types
export type {
  GeneratedComponent,
  ComponentPropertiesConfig,
  ComponentAnalysis,
  AIToolResult,
  EditorState,
  EditorActions,
  EditorStore,
  ComponentGenerationParams,
  PropertiesAnalysisParams,
  FieldGenerationResult
} from './types'

// Next.js Types
export type {
  NextJSLayout,
  NextJSPage,
  NextJSProject,
  LayoutGenerationParams,
  PageGenerationParams,
  NextJSMetadata,
  GeneratedNextJSStructure
} from './types/nextjs-types'

// Utilities
export { 
  analyzeComponentStructure, 
  extractPropsFromComponent, 
  generateComponentId, 
  validateComponentCode, 
  sanitizeComponentCode, 
  extractImportsFromComponent 
} from './utils/component-analyzer'

export {
  generateAppearanceFields,
  generateContentFields,
  generateBehaviorFields,
  generateDataFields,
  generateLayoutFields,
  generatePropertiesSchema,
  generateDefaultValues
} from './utils/properties-generator'

// Next.js Services
export { NextJSGenerator } from './services/nextjs-generator'
export { componentPersistence } from './services/component-persistence'

// Enhanced Agent-Based Services
export { agentOrchestrator } from './agents/agent-orchestrator'
export type { AgentTask, AgentResult, AgentCapability } from './agents/agent-orchestrator'

export {
  qualityValidationAgent,
  performanceOptimizationAgent,
  uxEnhancementAgent,
  errorRecoveryAgent,
  securityPrivacyAgent
} from './agents/specialized-agents'

export { enhancedGenerationService } from './services/enhanced-generation-service'
export type { EnhancedGenerationRequest, EnhancedGenerationResult } from './services/enhanced-generation-service'

export { errorRecoveryService } from './services/error-recovery-service'
export type { ErrorContext, RecoveryStrategy, ErrorAnalysis } from './services/error-recovery-service'

export { userExperienceService } from './services/user-experience-service'
export type {
  UserInteraction,
  UserJourney,
  UXInsights,
  PersonalizationProfile
} from './services/user-experience-service'

export { monitoringAnalyticsService } from './services/monitoring-analytics-service'
export type { SystemMetrics, Alert, AnalyticsInsight } from './services/monitoring-analytics-service'

// Enhanced Components
export { EnhancedAIVisualEditor } from './components/enhanced-ai-visual-editor'

// Legacy Shadcn Tools (kept for compatibility)
export {
  generateShadcnBlockTool,
  analyzeShadcnComponentTool,
  generateShadcnPropertiesTool,
  optimizeShadcnBlockTool,
  generateShadcnVariantsTool
} from './tools/shadcn-block-generation-tools'

// Intelligent Block Generation System
export { intelligentBlockService, IntelligentBlockService } from './services/intelligent-block-service'
export type {
  IntelligentBlockRequest,
  IntelligentBlockResult,
  CodebaseAnalysis,
  LearnedPatterns
} from './services/intelligent-block-service'

// Codebase Analyzer (Client-safe version)
export { codebaseAnalyzerClient, CodebaseAnalyzerClient } from './services/codebase-analyzer-client'
export type {
  CodebaseAnalysisOptions,
  ComponentUsagePatterns,
  ArchitecturalPatterns
} from './services/codebase-analyzer-client'

// Codebase Analyzer Types (Server-side only - use API routes for client access)
export type {
  CodebaseComponent,
  ImportInfo,
  ExportInfo,
  PropDefinition,
  UsagePattern,
  StylePattern,
  CompositionPattern,
  ComponentRelationship,
  ComponentCategory
} from './services/codebase-analyzer'

// Intelligent Generation Tools
export {
  generateIntelligentBlockTool,
  analyzeCodebasePatternsToolTool,
  generateContextAwareComponentTool
} from './tools/intelligent-block-generator'

// Demo Components
export { IntelligentBlockGeneratorDemo } from './components/intelligent-block-generator-demo'

// Note: Hooks are exported separately in './hooks' for client-side use only
// Import from '@/lib/ai-visual-editor/hooks' in client components
